import { OperationalServiceTypes } from "@iris/discovery.fe.client";
import type { AppConfig } from "@/types/AppConfig";

const w = window as any;

const config: AppConfig = {
  logLevel:
    import.meta.env.VITE_LOG_LEVEL || (w._env_ && w._env_.VITE_LOG_LEVEL),
  discoveryURL:
    import.meta.env.VITE_DISCOVERY_URL ||
    (w._env_ && w._env_.VITE_DISCOVERY_URL),
  api: {
    [OperationalServiceTypes.PortalService]: {
      //update here after creating a new OperationalServiceTypes
      getSubmittedFilesColumns: `api/files/submitted/columns`,
      getSubmittedFiles: `api/files/submitted`,
      portalFilesUpload: `api/files`,
    },
    [OperationalServiceTypes.UserService]: {
      getPortalMenu: `api/users/portal-menu`,
    },
    [OperationalServiceTypes.MasterDataService]: {
      getThemes: `api/themes`,
    },
  },
  inputDebounceInterval: 500,
  dateFormat: "YYYY-MM-DD",
  idleTime: 6000000,
  clientId:
    import.meta.env.VITE_OKTA_CLIENT_ID ||
    (w._env_ && w._env_.REACT_APP_OKTA_CLIENT_ID),
};

const changeApi: AppConfig = config;

export default changeApi;
