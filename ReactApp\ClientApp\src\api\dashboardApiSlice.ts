import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQueryWithReauth } from './interceptorsSlice';
import type { PortalMenuResponse } from '@/types/Dashboard';
import httpVerbs from '@/utils/http/httpVerbs';
import { OperationalServiceTypes } from "@iris/discovery.fe.client";
import config from '@/api/endpoints/endpoints';

export const dashboardApiSlice = createApi({
    reducerPath: '/dashboard',
    baseQuery: baseQueryWithReauth,
    endpoints: (builder) => ({
        getPortalMenu: builder.query<PortalMenuResponse, void>({
            query: () => ({
                url: config.api[OperationalServiceTypes.UserService].getPortalMenu,
                method: httpVerbs.GET,
                meta: OperationalServiceTypes.UserService,
            }),
        }),
    }),
});

export const { useGetPortalMenuQuery } = dashboardApiSlice;
