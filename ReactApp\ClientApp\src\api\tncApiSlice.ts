

//import { baseQueryWithReauth } from './interceptorsSlice';
import type { AcceptTncResponse, EvaluateTncResponse } from '@/types/TermsAndConditions';
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import httpVerbs from '@/utils/http/httpVerbs';
//import config from '@/config/app.config';

export const waiting = (ms: number): Promise<void> => {
    return new Promise((resolve) => setTimeout(resolve, ms));
};

const baseUrl = 'https://documentadministration.sandbox.irispme.com/NBT1';

export const tncApiSlice = createApi({
    reducerPath: '/tnc',
    baseQuery: fetchBaseQuery({
        baseUrl: baseUrl,
        prepareHeaders: (headers) => {

            headers.set("Authorization", "Bearer " + "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");

            return headers;
        },
    }),
    endpoints: (builder) => ({
        evaluateTnc: builder.query<EvaluateTncResponse, void>({
            query: () => ({
                url: `api/terms-and-conditions/evaluate`,
                headers: {
                    'Content-Type': 'application/json',

                },
                method: httpVerbs.GET,
            }),
        }),
        acceptTnc: builder.mutation<AcceptTncResponse, { termAndConditionId: number, triggerType: string }>({
            query: ({ termAndConditionId, triggerType }) => ({
                url: `api/terms-and-conditions/accept`,
                method: httpVerbs.POST,
                body: { termAndConditionId, triggerType },
            }),
        }),
        fetchTncDocument: builder.query<Blob, string>({
            query: (documentUrl) => ({
                url: `${documentUrl}`,
                responseHandler: (response) => response.blob(),
            }),
        }),
    })
});

export const { useEvaluateTncQuery, useAcceptTncMutation, useFetchTncDocumentQuery } = tncApiSlice;
