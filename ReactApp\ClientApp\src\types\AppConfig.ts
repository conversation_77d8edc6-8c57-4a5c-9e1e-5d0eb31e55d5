import { OperationalServiceTypes } from "@iris/discovery.fe.client";

type AppConfig = {
  logLevel: string;
  discoveryURL: string;
  api: {
    [OperationalServiceTypes.PortalService]: {
      //update here after creating a new OperationalServiceTypes
      getSubmittedFilesColumns: string;
      getSubmittedFiles: string;
      portalFilesUpload: string;
    };
    [OperationalServiceTypes.UserService]: {
      getPortalMenu: string;
    },
    [OperationalServiceTypes.MasterDataService]: {
      getThemes: string;
    };
  };
  inputDebounceInterval: number;
  dateFormat: string;
  idleTime: number;
  clientId: string;
};

export type { AppConfig };
