import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";
import type { RootState } from "./store";

interface OktaState {
  accessToken: string | null;
  idToken: string | null;
  userInfo: Record<string, any> | null;
  tenantCode?: string | null;
}

const initialState: OktaState = {
  accessToken: null,
  idToken: null,
  userInfo: null,
  tenantCode: null,
};

const oktaSlice = createSlice({
  name: "okta",
  initialState,
  reducers: {
    setOktaTokens: (
      state,
      action: PayloadAction<{ accessToken: string; idToken: string }>,
    ) => {
      state.accessToken = action.payload.idToken;
      state.idToken = action.payload.accessToken;
    },
    setOktaUserInfo: (state, action: PayloadAction<Record<string, any>>) => {
      state.userInfo = action.payload;
      state.tenantCode = action.payload?.tenantCode?.[0] ?? null;
    },
    clearOktaAuth: (state) => {
      state.accessToken = null;
      state.idToken = null;
      state.userInfo = null;
      state.tenantCode = null;
    },
  },
});

export const selectTenantCode = (state: RootState) =>
  state.okta.userInfo?.tenantCode?.[0];

export const { setOktaTokens, setOktaUserInfo, clearOktaAuth } =
  oktaSlice.actions;
export default oktaSlice.reducer;
