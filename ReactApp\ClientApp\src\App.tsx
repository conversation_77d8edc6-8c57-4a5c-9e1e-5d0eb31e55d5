import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom";
import { Security } from "@okta/okta-react";
import { OktaAuth } from "@okta/okta-auth-js";

import AppRoutes from "./routes/appRoutes";
import AppInitializer from "./AppInitializer";
import LoaderComponent from "./components/Loader/Loader";

import oktaConfig from "./oktaConfig";
import logger from "@/utils/logger";
import config from "./config";
import DiscoveryFEClient from "@iris/discovery.fe.client";
import { store } from "./store/store";
import { selectDiscoveryStatus } from "@/store/discoverySlice";
import { useAppSelector } from "@/store/hooks";
import DISCOVERY_STATUS from "@/constants/discoveryStatus";

// Export for potential usage in other modules
export const configStore = store;
export const DiscoveryClient = new DiscoveryFEClient(config.discoveryURL);

// Loading UI
const LoadingScreen = () => <LoaderComponent />;

// Error UI
const ErrorScreen = ({ error }: { error: string }) => (
  <div className="app-error">Authentication Error: {error}</div>
);

// Dynamically create OktaAuth instance
const createOktaAuth = async (): Promise<OktaAuth> => {
  const issuer = await DiscoveryClient.getOAuthURL();
  if (!issuer) throw new Error("OAuth issuer URL could not be determined.");

  return new OktaAuth({
    issuer,
    clientId: oktaConfig.clientId,
    redirectUri: oktaConfig.redirectUri,
    postLogoutRedirectUri: oktaConfig.postLogoutRedirectUri,
    scopes: oktaConfig.scopes,
    tokenManager: {
      autoRenew: oktaConfig.tokenManager?.autoRenew ?? true,
      secure: oktaConfig.tokenManager?.secure ?? true,
      storageKey: oktaConfig.tokenManager?.storageKey ?? "AP_AUTH_TOKEN",
    },
  });
};

const App = () => {
  const [oktaAuth, setOktaAuth] = useState<OktaAuth | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  const discoveryStatus = useSelector(selectDiscoveryStatus);
  const bootstrapStatus = useAppSelector((s) => s.bootstrap.status);

  useEffect(() => {
    const initializeOkta = async () => {
      try {
        const auth = await createOktaAuth();
        setOktaAuth(auth);
      } catch (err) {
        if (err instanceof Error) {
          logger.error("Error initializing OktaAuth", {
            message: err.message,
            stack: err.stack,
          });
          setError(err.message);
        } else {
          logger.error("Error initializing OktaAuth", { error: String(err) });
          setError("Unknown error");
        }
      } finally {
        setLoading(false);
      }
    };

    void initializeOkta();
  }, []);

  const isLoading =
    loading ||
    discoveryStatus === DISCOVERY_STATUS.LOADING ||
    bootstrapStatus === "loading";

  const hasError =
    !!error ||
    discoveryStatus === DISCOVERY_STATUS.ERROR ||
    bootstrapStatus === "error";

  if (isLoading) return <LoadingScreen />;
  if (hasError) return <ErrorScreen error={error || "Initialization failed"} />;

  return (
    <Security
      oktaAuth={oktaAuth!}
      restoreOriginalUri={async (_oktaAuth, originalUri) => {
        window.location.replace(originalUri || "/dashboard");
      }}
    >
      {/* Auth > Discovery > Initial APIs */}
      <AppInitializer />
      <AppRoutes />
    </Security>
  );
};

const AppWithRouterAccess = () => (
  <BrowserRouter>
    <App />
  </BrowserRouter>
);

export default AppWithRouterAccess;
