import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";

type Status = "idle" | "loading" | "success" | "error";

interface BootstrapState {
  status: Status;
  error?: string | null;
}

const initialState: BootstrapState = {
  status: "idle",
  error: null,
};

const bootstrapSlice = createSlice({
  name: "bootstrap",
  initialState,
  reducers: {
    setBootstrapLoading: (state) => {
      state.status = "loading";
      state.error = null;
    },
    setBootstrapSuccess: (state) => {
      state.status = "success";
      state.error = null;
    },
    setBootstrapError: (state, action: PayloadAction<string | null>) => {
      state.status = "error";
      state.error = action.payload ?? "Bootstrap failed";
    },
    resetBootstrap: () => initialState,
  },
});

export const {
  setBootstrapLoading,
  setBootstrapSuccess,
  setBootstrapError,
  resetBootstrap,
} = bootstrapSlice.actions;

export default bootstrapSlice.reducer;
