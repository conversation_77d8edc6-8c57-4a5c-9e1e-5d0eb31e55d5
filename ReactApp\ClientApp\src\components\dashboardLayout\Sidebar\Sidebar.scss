.sidebar {
  width: 260px;
  background-color: #24303b; // var(--kendo-color-custom-bar-background)
  color: var(--kendo-color-on-dark); // base text color
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  overflow: hidden;

  &--collapsed {
    width: 64px;

    .sidebar__label,
    .sidebar__submenu,
    .sidebar__gear,
    .sidebar__caret {
      display: none;
    }
  }

  &__container {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 100%;
  }

  &__scrollable {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    min-height: 0;
  }

  &__section {
    margin-bottom: 1.25rem;
  }

  &__item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.5rem;
    padding: 0.6rem 0.5rem;
    cursor: pointer;
    border-radius: 4px;

    &:hover {
      background-color: var(
        --kendo-color-dark-hover
      ); // slightly lighter on hover
    }

    &--active {
      background-color: var(--kendo-color-dark-hover);
      // color: var(--kendo-color-on-primary);

      .sidebar__icon {
        // color: var(--kendo-color-on-primary);
      }

      .sidebar__label {
        // color: var(--kendo-color-on-primary);
        // font-weight: 500;
      }
    }
  }

  &__item-left {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
  }

  &__icon {
    width: 20px;
    height: 20px;
    color: var(--kendo-color-on-dark-disabled); // subtle icon
  }

  &__subicon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    color: var(--kendo-color-on-dark-disabled);
  }

  &__collapse {
    background: none;
    border: none;
    color: var(--kendo-color-on-dark-disabled);
    cursor: pointer;

    svg {
      width: 18px;
      height: 18px;
    }
  }

  &__caret {
    width: 16px;
    height: 16px;
    color: var(--kendo-color-on-dark-disabled);
  }

  &__gear {
    width: 16px;
    height: 16px;
    color: var(--kendo-color-on-dark-disabled);
    margin-right: 0.5rem;
    cursor: pointer;

    &:hover {
      color: var(--kendo-color-on-dark); // highlight on hover
    }
  }

  &__submenu {
    margin-top: 0.25rem;
    padding-left: 1.75rem;
    list-style: none;

    li {
      display: flex;
      align-items: center;
      font-size: 0.85rem;
      padding: 0.35rem 0;
      color: var(--kendo-color-on-dark-disabled);
      cursor: pointer;
      border-radius: 4px;

      &:hover {
        color: var(--kendo-color-on-dark);
        background-color: var(--kendo-color-dark-hover);
      }
    }
  }

  &__right-icons {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  &__label {
    font-size: 0.95rem;
    color: var(--kendo-color-on-dark);
  }

  &__footer {
    padding: 1rem;
    border-top: 0.1rem solid var(--kendo-color-on-dark);
    background-color: #24303b; // var(--kendo-color-custom-bar-background)
    transition: background-color 0.2s ease;
    cursor: pointer;

    &:hover {
      background-color: var(--kendo-color-dark-hover);
    }

    &--active {
      background-color: var(--kendo-color-dark-hover);
    }
  }

  &__footer-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--kendo-color-on-dark-disabled);
    cursor: pointer;

    &:hover {
      color: var(--kendo-color-on-dark);
    }
  }

  &__footer-icon {
    width: 18px;
    height: 18px;
    color: var(--kendo-color-on-dark-disabled);
  }

  &__footer-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  &__footer-text {
    line-height: 1;
    font-size: 0.9rem;
    color: inherit;
  }
}
