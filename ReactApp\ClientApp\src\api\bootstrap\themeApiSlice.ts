import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "@/api/interceptorsSlice";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";
import config from "@/api/endpoints/endpoints";
import httpVerbs from "@/utils/http/httpVerbs";

interface CurrentUser {
  id: string;
  email: string;
  displayName: string;
  roles: string[];
}

export const themeApiSlice = createApi({
  reducerPath: "themeApi",
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    getCurrentUser: builder.query<CurrentUser, void>({
      query: () => ({
        url: config.api[OperationalServiceTypes.MasterDataService].getThemes,
        method: httpVerbs.GET,
        meta: OperationalServiceTypes.MasterDataService,
      }),
    }),
  }),
});

export const { useGetCurrentUserQuery, util: userApiUtil } = themeApiSlice;
