import { Outlet } from 'react-router-dom';
import { Sidebar } from '@/components/dashboardLayout/Sidebar';
import { TopBar } from '@/components/dashboardLayout/TopBar';
import { useSidebar } from '@/hooks/useSidebar';
import LoaderComponent from '@/components/Loader/Loader';
// import { Footer } from '@/components/dashboardLayout/Footer';
import './DashboardLayout.scss';

const DashboardLayout = ({ children }: { children?: React.ReactNode }) => {
  const { isLoading, isReady } = useSidebar();

  return (
    <div className="dashboard-layout">
      <TopBar />

      <div className="dashboard-layout__body">
        <Sidebar />

        <div className="dashboard-layout__main">
          <main className="dashboard-layout__content">
            {isLoading && (
              <LoaderComponent />
            )}

            {isReady && (children || <Outlet />)}
          </main>
          {/* <Footer /> */}
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout;
