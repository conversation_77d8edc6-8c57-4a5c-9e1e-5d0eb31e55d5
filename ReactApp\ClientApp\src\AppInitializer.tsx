import { useEffect, useCallback } from "react";
import { useOktaAuth } from "@okta/okta-react";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { DiscoveryClient } from "@/App";
import {
  setBaseUrls,
  setDiscoveryLoading,
  setDiscoveryError,
  selectDiscoveryStatus,
} from "@/store/discoverySlice";
import { setOktaTokens, setOktaUserInfo } from "@/store/oktaSlice";
import {
  setBootstrapLoading,
  setBootstrapSuccess,
  setBootstrapError,
} from "@/store/bootstrapSlice";
import DISCOVERY_STATUS from "@/constants/discoveryStatus";
import { themeApiSlice } from "@/api/bootstrap/themeApiSlice";
import logger from "@/utils/logger";
import { dashboardApiSlice } from "./api/bootstrap/dashboardApiSlice";

const AppInitializer = () => {
  const dispatch = useAppDispatch();

  const discoveryStatus = useAppSelector(selectDiscoveryStatus);
  const bootstrapStatus = useAppSelector((s) => s.bootstrap.status);
  const tenant = useAppSelector((s) => s.okta.tenantCode);

  const { oktaAuth, authState } = useOktaAuth();

  // 1) Capture tokens & user when authenticated
  useEffect(() => {
    const captureTokensAndUser = async () => {
      if (!authState?.isAuthenticated) return;

      try {
        const tokens = await oktaAuth.tokenManager.getTokens();
        const idToken = tokens.idToken?.idToken ?? "";
        const accessToken = tokens.accessToken?.accessToken ?? "";

        dispatch(setOktaTokens({ accessToken, idToken }));

        const claims = await oktaAuth.getUser();
        dispatch(setOktaUserInfo(claims));
        logger.info("AppInitializer: Okta tokens and user claims captured");
      } catch (error) {
        logger.error("AppInitializer: Failed to retrieve Okta tokens/user", {
          error,
        });
      }
    };

    void captureTokensAndUser();
  }, [authState?.isAuthenticated, oktaAuth, dispatch]);

  // 2) Discovery: once token+tenant exist and discovery is IDLE
  const fetchDiscovery = useCallback(async () => {
    try {
      dispatch(setDiscoveryLoading());
      logger.info("AppInitializer: Fetching base URLs for tenant", { tenant });

      const discoveryEndpoints = await DiscoveryClient.getBaseURL(
        "AP_AUTH_TOKEN",
        {
          TenantCode: tenant ?? undefined,
        }
      );

      if (Array.isArray(discoveryEndpoints) && discoveryEndpoints.length > 0) {
        dispatch(setBaseUrls(discoveryEndpoints));
        logger.info("AppInitializer: Discovery success", {
          count: discoveryEndpoints.length,
        });
      } else {
        logger.error("AppInitializer: Invalid discovery result");
        dispatch(setDiscoveryError());
      }
    } catch (error) {
      logger.error("AppInitializer: Discovery service failed", { error });
      dispatch(setDiscoveryError());
    }
  }, [dispatch, tenant]);

  useEffect(() => {
    const readyForDiscovery =
      authState?.isAuthenticated &&
      !!tenant &&
      discoveryStatus === DISCOVERY_STATUS.IDLE;

    if (readyForDiscovery) {
      void fetchDiscovery();
    }
  }, [authState?.isAuthenticated, tenant, discoveryStatus, fetchDiscovery]);

  // 3) Bootstrap/ initial APIs AFTER discovery success
  useEffect(() => {
    const doBootstrap = async () => {
      if (bootstrapStatus !== "idle") return;
      if (discoveryStatus !== DISCOVERY_STATUS.SUCCESS || !tenant) return;

      dispatch(setBootstrapLoading());
      try {
        const promises: Promise<any>[] = [];

        // initial themes API
        promises.push(
          dispatch(themeApiSlice.endpoints.getCurrentUser.initiate()).unwrap()
        );

        // initial dashboard API
        promises.push(
          dispatch(dashboardApiSlice.endpoints.getPortalMenu.initiate()).unwrap()
        );

        // Add more initial apis here... like evaluate/portal-menu

        await Promise.all(promises);

        dispatch(setBootstrapSuccess());
        logger.info("AppInitializer: Bootstrap success");
      } catch (err) {
        logger.error("AppInitializer: Bootstrap failed", { err });
        dispatch(
          setBootstrapError(
            err instanceof Error ? err.message : "Bootstrap failed"
          )
        );
      }
    };

    void doBootstrap();
  }, [bootstrapStatus, discoveryStatus, tenant, dispatch]);

  return null;
};

export default AppInitializer;
